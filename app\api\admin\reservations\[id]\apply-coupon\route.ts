import { logAdminAction, withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

interface ApplyCouponRequest {
	couponCode: string;
	serviceId?: string;
}

interface ApplyCouponResponse {
	success: boolean;
	reservation?: {
		id: string;
		total_amount: number;
		discount_code: string | null;
		discount_amount: number;
	};
	coupon?: {
		id: string;
		code: string;
		description: string | null;
		discount_type: string;
		discount_value: number;
	};
	discountAmount?: number;
	originalAmount?: number;
	finalAmount?: number;
	error?: string;
}

// POST /api/admin/reservations/[id]/apply-coupon - Apply discount coupon to reservation
export const POST = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const reservationId = params.id;
		const { couponCode, serviceId }: ApplyCouponRequest = await request.json();

		if (!couponCode) {
			return NextResponse.json<ApplyCouponResponse>(
				{
					success: false,
					error: "Code de réduction requis",
				},
				{ status: 400 }
			);
		}

		// Get current reservation
		const { data: reservation, error: reservationError } = await supabaseAdmin
			.from("reservations")
			.select("*")
			.eq("id", reservationId)
			.single();

		if (reservationError || !reservation) {
			return NextResponse.json<ApplyCouponResponse>(
				{
					success: false,
					error: "Réservation non trouvée",
				},
				{ status: 404 }
			);
		}

		// Calculate original amount (before any existing discount)
		const originalAmount = reservation.total_amount + reservation.discount_amount;

		// Get the discount coupon
		const { data: coupon, error: couponError } = await supabaseAdmin
			.from("discount_coupons")
			.select("*")
			.eq("code", couponCode.toUpperCase())
			.eq("is_active", true)
			.single();

		if (couponError || !coupon) {
			return NextResponse.json<ApplyCouponResponse>({
				success: false,
				error: "Code de réduction invalide ou expiré",
			});
		}

		// Check if coupon is within valid date range
		const now = new Date();
		if (coupon.valid_from && new Date(coupon.valid_from) > now) {
			return NextResponse.json<ApplyCouponResponse>({
				success: false,
				error: "Ce code de réduction n'est pas encore valide",
			});
		}

		if (coupon.valid_until && new Date(coupon.valid_until) < now) {
			return NextResponse.json<ApplyCouponResponse>({
				success: false,
				error: "Ce code de réduction a expiré",
			});
		}

		// Check usage limit
		if (coupon.usage_limit && coupon.current_usage >= coupon.usage_limit) {
			return NextResponse.json<ApplyCouponResponse>({
				success: false,
				error: "Ce code de réduction a atteint sa limite d'utilisation",
			});
		}

		// Check if coupon applies to the specific service
		const targetServiceId = serviceId || reservation.service_id;
		if (targetServiceId && coupon.applicable_services && coupon.applicable_services.length > 0) {
			if (!coupon.applicable_services.includes(targetServiceId)) {
				return NextResponse.json<ApplyCouponResponse>({
					success: false,
					error: "Ce code de réduction ne s'applique pas à ce service",
				});
			}
		}

		// Check minimum purchase amount
		if (coupon.min_purchase_amount && originalAmount < coupon.min_purchase_amount) {
			return NextResponse.json<ApplyCouponResponse>({
				success: false,
				error: `Montant minimum requis: ${coupon.min_purchase_amount}€`,
			});
		}

		// Calculate discount amount
		let discountAmount = 0;
		if (coupon.discount_type === "percentage") {
			discountAmount = (originalAmount * coupon.discount_value) / 100;
		} else if (coupon.discount_type === "fixed") {
			discountAmount = coupon.discount_value;
		}

		// Apply maximum discount limit if set
		if (coupon.max_discount_amount && discountAmount > coupon.max_discount_amount) {
			discountAmount = coupon.max_discount_amount;
		}

		// Ensure discount doesn't exceed total amount
		if (discountAmount > originalAmount) {
			discountAmount = originalAmount;
		}

		const finalAmount = Math.max(0, originalAmount - discountAmount);

		// Update reservation with new discount
		const { data: updatedReservation, error: updateError } = await supabaseAdmin
			.from("reservations")
			.update({
				discount_code: coupon.code,
				discount_amount: discountAmount,
				total_amount: finalAmount,
				updated_at: new Date().toISOString(),
			})
			.eq("id", reservationId)
			.select()
			.single();

		if (updateError) {
			console.error("Error updating reservation with coupon:", updateError);
			return NextResponse.json<ApplyCouponResponse>(
				{
					success: false,
					error: "Erreur lors de l'application du code de réduction",
				},
				{ status: 500 }
			);
		}

		// Update coupon usage count if this is a new application
		if (reservation.discount_code !== coupon.code) {
			const { error: couponUpdateError } = await supabaseAdmin
				.from("discount_coupons")
				.update({
					current_usage: coupon.current_usage + 1,
					updated_at: new Date().toISOString(),
				})
				.eq("id", coupon.id);

			if (couponUpdateError) {
				console.error("Error updating coupon usage:", couponUpdateError);
				// Don't fail the request if coupon update fails
			}
		}

		// Log admin action
		await logAdminAction(
			user.id,
			"update",
			"reservations",
			reservationId,
			{
				discount_code: reservation.discount_code,
				discount_amount: reservation.discount_amount,
				total_amount: reservation.total_amount,
			},
			{
				discount_code: coupon.code,
				discount_amount: discountAmount,
				total_amount: finalAmount,
			},
			request
		);

		return NextResponse.json<ApplyCouponResponse>({
			success: true,
			reservation: {
				id: updatedReservation.id,
				total_amount: updatedReservation.total_amount,
				discount_code: updatedReservation.discount_code,
				discount_amount: updatedReservation.discount_amount,
			},
			coupon: {
				id: coupon.id,
				code: coupon.code,
				description: coupon.description,
				discount_type: coupon.discount_type,
				discount_value: coupon.discount_value,
			},
			discountAmount,
			originalAmount,
			finalAmount,
		});
	} catch (error) {
		console.error("Apply coupon error:", error);
		return NextResponse.json<ApplyCouponResponse>(
			{
				success: false,
				error: "Erreur interne du serveur",
			},
			{ status: 500 }
		);
	}
}, "reservations:write");

// DELETE /api/admin/reservations/[id]/apply-coupon - Remove discount coupon from reservation
export const DELETE = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const reservationId = params.id;

		// Get current reservation
		const { data: reservation, error: reservationError } = await supabaseAdmin
			.from("reservations")
			.select("*")
			.eq("id", reservationId)
			.single();

		if (reservationError || !reservation) {
			return NextResponse.json<ApplyCouponResponse>(
				{
					success: false,
					error: "Réservation non trouvée",
				},
				{ status: 404 }
			);
		}

		if (!reservation.discount_code) {
			return NextResponse.json<ApplyCouponResponse>({
				success: false,
				error: "Aucun code de réduction appliqué à cette réservation",
			});
		}

		// Calculate original amount (before discount)
		const originalAmount = reservation.total_amount + reservation.discount_amount;

		// Update reservation to remove discount
		const { data: updatedReservation, error: updateError } = await supabaseAdmin
			.from("reservations")
			.update({
				discount_code: null,
				discount_amount: 0,
				total_amount: originalAmount,
				updated_at: new Date().toISOString(),
			})
			.eq("id", reservationId)
			.select()
			.single();

		if (updateError) {
			console.error("Error removing coupon from reservation:", updateError);
			return NextResponse.json<ApplyCouponResponse>(
				{
					success: false,
					error: "Erreur lors de la suppression du code de réduction",
				},
				{ status: 500 }
			);
		}

		// Decrease coupon usage count
		const { error: couponUpdateError } = await supabaseAdmin
			.from("discount_coupons")
			.update({
				current_usage: Math.max(
					0,
					(
						await supabaseAdmin
							.from("discount_coupons")
							.select("current_usage")
							.eq("code", reservation.discount_code.toUpperCase())
							.single()
					).data?.current_usage - 1 || 0
				),
				updated_at: new Date().toISOString(),
			})
			.eq("code", reservation.discount_code.toUpperCase());

		if (couponUpdateError) {
			console.error("Error updating coupon usage:", couponUpdateError);
			// Don't fail the request if coupon update fails
		}

		// Log admin action
		await logAdminAction(
			user.id,
			"update",
			"reservations",
			reservationId,
			{
				discount_code: reservation.discount_code,
				discount_amount: reservation.discount_amount,
				total_amount: reservation.total_amount,
			},
			{
				discount_code: null,
				discount_amount: 0,
				total_amount: originalAmount,
			},
			request
		);

		return NextResponse.json<ApplyCouponResponse>({
			success: true,
			reservation: {
				id: updatedReservation.id,
				total_amount: updatedReservation.total_amount,
				discount_code: updatedReservation.discount_code,
				discount_amount: updatedReservation.discount_amount,
			},
			originalAmount,
			finalAmount: originalAmount,
		});
	} catch (error) {
		console.error("Remove coupon error:", error);
		return NextResponse.json<ApplyCouponResponse>(
			{
				success: false,
				error: "Erreur interne du serveur",
			},
			{ status: 500 }
		);
	}
}, "reservations:write");
